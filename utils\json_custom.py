import json
import uuid
from typing import Any
from decimal import Decimal
from datetime import date, datetime


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        if isinstance(obj, Decimal):
            return str(obj)  # Using string representation to preserve precision
        return json.JSONEncoder.default(self, obj)


def custom_json_serializer(obj: Any) -> str:
    """
    Custom JSON serializer function to be used by SQLAlchemy.
    """
    return json.dumps(obj, cls=CustomJSONEncoder)
