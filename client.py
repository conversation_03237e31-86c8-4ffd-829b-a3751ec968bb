import asyncio
from typing import cast
from fastmcp import Client

from fastmcp.client.client import <PERSON><PERSON><PERSON><PERSON><PERSON>ult

from action_server.contracts.provably.collections import CollectionListRequest, CollectionQueryParams

client = Client("http://127.0.0.1:8000/pmcp")

# async def call_tool(name: str):
#     async with client:
#         result = await client.call_tool("send_hello", {"name": name})
#         print(result)

# asyncio.run(call_tool("11111111111111"))


async def test_list_collections():
    """Test listing collections through PMCP Action Server."""


    # Prepare request (API key will come from headers via middleware)
    query_params=CollectionQueryParams(
        page=0,
        page_size=5,
        query="basketball"
    )

    try:
        async with client:
            # Call the provably_list_collections tool
            # Note: API key should be provided via headers when middleware is implemented
            result = await client.call_tool(
                "provably_list_collections",
                arguments={"query_params": query_params.model_dump()}
            )
            
            result = cast(CallToolResult, result)

            print("Collections retrieved successfully:")
            print(f"{type(result)=}")
            print(result.data)

    except Exception as e:
        print(f"Error calling Provably API: {e}")
        
        
asyncio.run(test_list_collections())