## Provably API Endpoints

This document outlines the endpoints of the Provably API, categorized by their respective tags. Each endpoint includes a checkbox for tracking implementation progress, along with its request and response schemas in Pydantic BaseModel format.

---

### Collection

- [ ] **open_list_collections**
    - **Description:** List collections with optional filtering and pagination.
    - **Method:** GET
    - **Path:** `/api/v1/open/collections/`
    - **Request Schema:** `CollectionQueryParams` (Query Parameters)
    - **Response Schema:** `list[CollectionResponse]`

- [ ] **open_create_collection**
    - **Description:** Create a new collection.
    - **Method:** POST
    - **Path:** `/api/v1/open/collections/`
    - **Request Schema:** `CollectionCreateParams` (Body)
    - **Response Schema:** `CollectionResponseId`

- [ ] **open_bulk_delete_collections**
    - **Description:** Bulk delete collections.
    - **Method:** POST
    - **Path:** `/api/v1/open/collections/bulk_delete`
    - **Request Schema:** `CollectionBulkDeleteParams` (Body)
    - **Response Schema:** `CollectionBulkDeleteResponse`

- [ ] **open_bulk_duplicate_collections**
    - **Description:** Bulk duplicate collections.
    - **Method:** POST
    - **Path:** `/api/v1/open/collections/bulk_duplicate`
    - **Request Schema:** `CollectionBulkDuplicateParams` (Body)
    - **Response Schema:** `CollectionBulkDuplicateResponse`

- [ ] **open_get_collection_by_id**
    - **Description:** Get a collection by its ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/collections/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `CollectionResponse`

- [ ] **open_delete_collection**
    - **Description:** Delete a collection by its ID.
    - **Method:** DELETE
    - **Path:** `/api/v1/open/collections/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `None` (200 OK with no content)

- [ ] **open_update_collection**
    - **Description:** Update an existing collection by its ID.
    - **Method:** PATCH
    - **Path:** `/api/v1/open/collections/{id}`
    - **Request Schema:** `CollectionUpdateParams` (Body)
    - **Response Schema:** `CollectionResponseId`

- [ ] **open_delete_collection_access**
    - **Description:** Delete collection access.
    - **Method:** DELETE
    - **Path:** `/api/v1/open/collections/{id}/access`
    - **Request Schema:** `None`
    - **Response Schema:** `None` (200 OK with no content)

- [ ] **open_update_collection_access**
    - **Description:** Update collection access.
    - **Method:** PATCH
    - **Path:** `/api/v1/open/collections/{id}/access`
    - **Request Schema:** `CollectionAccessUpdateParams` (Body)
    - **Response Schema:** `CollectionResponseId`

- [ ] **open_get_collection_columns**
    - **Description:** Get columns for a specific collection.
    - **Method:** GET
    - **Path:** `/api/v1/open/collections/{id}/columns`
    - **Request Schema:** `CollectionColumnsListQueryParams` (Query Parameters)
    - **Response Schema:** `list[CollectionColumnsListResponse]`

- [ ] **open_get_collection_users**
    - **Description:** Get users associated with a collection.
    - **Method:** GET
    - **Path:** `/api/v1/open/collections/{id}/users`
    - **Request Schema:** `CollectionUsersListQueryParams` (Query Parameters)
    - **Response Schema:** `list[CollectionUserInvitationResponse]`

- [ ] **open_invite_users_to_collection**
    - **Description:** Invite users to a collection.
    - **Method:** POST
    - **Path:** `/api/v1/open/collections/{id}/users`
    - **Request Schema:** `CollectionUserInvitationParams` (Body)
    - **Response Schema:** `None` (200 OK with no content)

- [ ] **open_delete_collection_invitation**
    - **Description:** Delete a user invitation from a collection.
    - **Method:** DELETE
    - **Path:** `/api/v1/open/collections/{id}/users`
    - **Request Schema:** `CollectionUserInvitationParams` (Body)
    - **Response Schema:** `None` (200 OK with no content)

### Chatbot

- [ ] **conversion**
    - **Description:** Chatbot conversation.
    - **Method:** POST
    - **Path:** `/api/v1/open/conversation/`
    - **Request Schema:** `LLMConversationParams` (Body)
    - **Response Schema:** `None` (200 OK with no content)

- [ ] **openConversionHistory**
    - **Description:** Chatbot conversation history.
    - **Method:** GET
    - **Path:** `/api/v1/open/conversation/history`
    - **Request Schema:** `None`
    - **Response Schema:** `list[LLMConversationHistoryResponse]`

- [ ] **openVerifyConversation**
    - **Description:** Verify conversation.
    - **Method:** POST
    - **Path:** `/api/v1/open/conversation/{id}/verify`
    - **Request Schema:** `None`
    - **Response Schema:** `LLMConversationVerifyResponse`

### Credits

- [ ] **openGetUserCreditInfo**
    - **Description:** Get user credit info.
    - **Method:** GET
    - **Path:** `/api/v1/open/credits/`
    - **Request Schema:** `None`
    - **Response Schema:** `CreditsResponse`

- [ ] **openRequestCredits**
    - **Description:** Request credits.
    - **Method:** POST
    - **Path:** `/api/v1/open/credits/request`
    - **Request Schema:** `RequestCreditsParams` (Body)
    - **Response Schema:** `RequestCreditsResponse`

### Data source

- [ ] **open_list_data_source**
    - **Description:** List all data sources.
    - **Method:** GET
    - **Path:** `/api/v1/open/data_sources/`
    - **Request Schema:** `None`
    - **Response Schema:** `list[DataSourceResponse]`

- [ ] **open_create_data_source**
    - **Description:** Create a new data source.
    - **Method:** POST
    - **Path:** `/api/v1/open/data_sources/`
    - **Request Schema:** `DataSourceParams` (Body)
    - **Response Schema:** `DataSourceResponse`

- [ ] **open_get_by_id_data_source**
    - **Description:** Get a data source by its ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/data_sources/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `DataSourceResponse`

- [ ] **open_remove_data_source**
    - **Description:** Remove a data source by its ID.
    - **Method:** DELETE
    - **Path:** `/api/v1/open/data_sources/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `DataSourceResponseId`

- [ ] **open_update_data_source**
    - **Description:** Update an existing data source by its ID.
    - **Method:** PATCH
    - **Path:** `/api/v1/open/data_sources/{id}`
    - **Request Schema:** `DataSourceParams` (Body)
    - **Response Schema:** `DataSourceResponseId`

- [ ] **open_test_connection_data_source**
    - **Description:** Test connection to data source.
    - **Method:** GET
    - **Path:** `/api/v1/open/data_sources/{id}/connect`
    - **Request Schema:** `None`
    - **Response Schema:** `DataSourceResponseNoSchema`

### Proof request

- [ ] **open_list_proof_request**
    - **Description:** List all proof requests.
    - **Method:** GET
    - **Path:** `/api/v1/open/proof_requests/`
    - **Request Schema:** `None`
    - **Response Schema:** `list[list[ProofRequestResponse]]`

- [ ] **open_create_proof_request**
    - **Description:** Create a new proof request.
    - **Method:** POST
    - **Path:** `/api/v1/open/proof_requests/`
    - **Request Schema:** `ProofRequestParams` (Body)
    - **Response Schema:** `list[ProofRequestResponseId]`

- [ ] **open_inject_data**
    - **Description:** Inject data and create a proof request.
    - **Method:** POST
    - **Path:** `/api/v1/open/proof_requests/inject_data`
    - **Request Schema:** `ProofOnInjectedDataRequestParams` (Body)
    - **Response Schema:** `list[ProofRequestResponseId]`

- [ ] **open_get_by_id_proof_request**
    - **Description:** Get a proof request by its ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/proof_requests/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `list[ProofRequestResponse]`

- [ ] **open_cancel_proof_request**
    - **Description:** Cancel a proof request by its ID.
    - **Method:** PUT
    - **Path:** `/api/v1/open/proof_requests/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `list[ProofRequestResponseId]`

- [ ] **open_download_proof_bytes**
    - **Description:** Download proof bytes for a proof request.
    - **Method:** GET
    - **Path:** `/api/v1/open/proof_requests/{id}/download`
    - **Request Schema:** `None`
    - **Response Schema:** Returns proof bytes as a text file (200 OK)

### Proof verification

- [ ] **open_list_proof_verification**
    - **Description:** List all proof verifications.
    - **Method:** GET
    - **Path:** `/api/v1/open/proof_verifications/`
    - **Request Schema:** `None`
    - **Response Schema:** `list[list[ProofVerificationResponse]]`

- [ ] **open_create_proof_verification**
    - **Description:** Create a new proof verification.
    - **Method:** POST
    - **Path:** `/api/v1/open/proof_verifications/`
    - **Request Schema:** `ProofVerificationParams` (Body)
    - **Response Schema:** `list[ProofVerificationResponseId]`

- [ ] **open_get_by_id_proof_verification**
    - **Description:** Get a proof verification by its ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/proof_verifications/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `list[ProofVerificationResponse]`

- [ ] **open_cancel_proof_verification**
    - **Description:** Cancel a proof verification by its ID.
    - **Method:** PUT
    - **Path:** `/api/v1/open/proof_verifications/{id}/cancel`
    - **Request Schema:** `None`
    - **Response Schema:** `list[ProofVerificationResponseId]`

### Query

- [ ] **open_validate**
    - **Description:** Validate a SQL statement against a data source.
    - **Method:** GET
    - **Path:** `/api/v1/open/query/`
    - **Request Schema:** `ValidateQueryParams` (Query Parameters)
    - **Response Schema:** `list[ValidateResponse]`

### Source table

- [ ] **open_create**
    - **Description:** Create a new source table.
    - **Method:** POST
    - **Path:** `/api/v1/open/source_tables/`
    - **Request Schema:** `SourceTableCreateParams` (Body)
    - **Response Schema:** 200 - `SourceTableResponseId`; 400 - ValidationErrorResponse

- [ ] **open_list_by_data_source_id**
    - **Description:** List source tables by data source ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/source_tables/data_source/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `list[SourceTableResponse]`

- [ ] **open_get_by_id**
    - **Description:** Get a source table by its ID.
    - **Method:** GET
    - **Path:** `/api/v1/open/source_tables/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `SourceTableResponse`

- [ ] **open_remove**
    - **Description:** Remove a source table by its ID.
    - **Method:** DELETE
    - **Path:** `/api/v1/open/source_tables/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `SourceTableResponseId`

- [ ] **update_source_table**
    - **Description:** Update an existing source table by its ID.
    - **Method:** PATCH
    - **Path:** `/api/v1/open/source_tables/{id}`
    - **Request Schema:** `SourceTableUpdateParams` (Body)
    - **Response Schema:** 200 - `SourceTableResponse`; 400 - `ValidationErrorResponse`

- [ ] **openCheckConnectionSourceTable**
    - **Description:** Check the connection of a source table and update its validity.
    - **Method:** GET
    - **Path:** `/api/v1/open/source_tables/{id}/connect`
    - **Request Schema:** `None`
    - **Response Schema:** 200 - `SourceTableResponse`; 400 - `ValidationErrorResponse`

### Statistics

- [ ] **openGetTotalStats**
    - **Description:** Get total statistics for the current user.
    - **Method:** GET
    - **Path:** `/api/v1/open/stats/`
    - **Request Schema:** `None`
    - **Response Schema:** `StatsResponse`

- [ ] **openGetCollectionStats**
    - **Description:** Get statistics for a specific collection.
    - **Method:** GET
    - **Path:** `/api/v1/open/stats/collection/{id}`
    - **Request Schema:** `None`
    - **Response Schema:** `StatsResponse`

### User

- [ ] **open_current**
    - **Description:** Get current user information.
    - **Method:** GET
    - **Path:** `/api/v1/open/user/current`
    - **Request Schema:** `None`
    - **Response Schema:** `list[CurrentResponse]`

- [ ] **open_key**
    - **Description:** Get user API key.
    - **Method:** GET
    - **Path:** `/api/v1/open/user/key`
    - **Request Schema:** `None`
    - **Response Schema:** `list[ApiKeyResponse]`

---

Pydantic Python Request and Response Models:

```python
from enum import Enum
from typing import List, Optional, Union, Dict, Literal
from uuid import UUID
from pydantic import BaseModel, Field


# Enums
class AccessType(str, Enum):
    DRAFT = "draft"
    PUBLIC = "public"
    LIMITED_ACCESS = "limited_access"

class KoronFunction(str, Enum):
    SUM = "Sum"
    COUNT = "Count"
    AVERAGE = "Average"
    MEDIAN = "Median"
    VARIANCE = "Variance"
    STANDARD_DEVIATION = "StandardDeviation"
    MIN = "Min"
    MAX = "Max"

class ColumnType(str, Enum):
    INTEGER = "integer"
    BIGINT = "bigint"
    SMALLINT = "smallint"
    DECIMAL = "decimal"
    TIME = "time"
    TIMESTAMP = "timestamp"
    DATE = "date"
    BOOLEAN = "boolean"
    UUID = "uuid"
    STRING = "string"

class InvitationStatus(str, Enum):
    JOINED = "joined"
    NOT_JOINED = "not_joined"

class Provider(str, Enum):
    POSTGRESQL = "postgresql"

class SourceTableValidationIssue(str, Enum):
    SCHEMA_NOT_FOUND = "schema_not_found"
    TABLE_NOT_FOUND = "table_not_found"
    CONNECTION_ERROR = "connection_error"

class SortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"

class CollectionSortBy(str, Enum):
    NAME = "name"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    QUERY_COUNT = "query_count"
    COLUMN_COUNT = "column_count"
    ROW_COUNT = "row_count"
    CREDITS_EARNED = "credits_earned"

class CollectionColumnsSortBy(str, Enum):
    NAME = "name"
    TYPE = "type"

class CollectionUsersSortBy(str, Enum):
    EMAIL = "email"
    STATUS = "status"

class SourceTableColumnInvalidFieldType(str, Enum):
    NAME = "name"
    TYPE = "type"

class InvalidDependency(str, Enum):
    SOURCE_TABLE = "source_table"
    DATA_SOURCE = "data_source"

class LLMCollectionType(str, Enum):
    PUBLIC = "public"
    SHARED_WITH_ME = "shared_with_me"
    MINE_PUBLIC = "mine_public"
    MINE_LIMITIED_ACCESS = "mine_limited_access"

class LLMAnswer(BaseModel):
    text: str
    value: str
    query: str
    answered_at: str
    error_message: Optional[str]

class Status(str, Enum):
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"

class LLMProof(BaseModel):
   id: UUID
   status: Status


class LLMMetadata(BaseModel):
    collection_types: list[LLMCollectionType]
    is_proof_generated: bool
    cost: Optional[float]
    is_query_owner: bool
    is_web_search_enabled: bool

class LLMSource(BaseModel):
    id: UUID
    name: str
    type: AccessType

# Helper models for CompareOp
class CompareOpLt(BaseModel):
    type: Literal["Lt"]
    value: str

class CompareOpLtEq(BaseModel):
    type: Literal["LtEq"]
    value: str

class CompareOpGt(BaseModel):
    type: Literal["Gt"]
    value: str

class CompareOpGtEq(BaseModel):
    type: Literal["GtEq"]
    value: str

class CompareOpEq(BaseModel):
    type: Literal["Eq"]
    value: str

class CompareOpNotEq(BaseModel):
    type: Literal["NotEq"]
    value: str

class CompareOpIsNull(BaseModel):
    type: Literal["IsNull"]

class CompareOpIsNotNull(BaseModel):
    type: Literal["IsNotNull"]

class CompareOpIsTrue(BaseModel):
    type: Literal["IsTrue"]

class CompareOpIsNotTrue(BaseModel):
    type: Literal["IsNotTrue"]

class CompareOpIsFalse(BaseModel):
    type: Literal["IsFalse"]

class CompareOpIsNotFalse(BaseModel):
    type: Literal["IsNotFalse"]

CompareOp = Union[
    CompareOpLt, CompareOpLtEq, CompareOpGt, CompareOpGtEq,
    CompareOpEq, CompareOpNotEq, CompareOpIsNull, CompareOpIsNotNull,
    CompareOpIsTrue, CompareOpIsNotTrue, CompareOpIsFalse, CompareOpIsNotFalse
]

# Schemas
class Aggregation(BaseModel):
    function: KoronFunction
    column: str
    alias: Optional[str] = None

class AggregationWithColumnType(Aggregation):
    column_type: ColumnType

class ApiKeyResponse(BaseModel):
    api_key: str

class CollectionAccessUpdateParams(BaseModel):
    access_type: AccessType

class CollectionBulkDeleteParams(BaseModel):
    ids: List[UUID]

class CollectionBulkDeleteResponse(BaseModel):
    ids: List[UUID]

class CollectionBulkDuplicateParams(BaseModel):
    ids: List[UUID]

class CollectionBulkDuplicateResponse(BaseModel):
    ids: List[UUID]

class CollectionColumnResponse(BaseModel):
    id: UUID
    name: str
    type: ColumnType
    description: str
    invalid_field: Optional[SourceTableColumnInvalidFieldType] = None

class CollectionColumnsListResponse(BaseModel):
    columns: List['SourceTableColumnResponse']

class CollectionCreateParams(BaseModel):
    name: str
    description: str
    data_source_id: UUID
    source_table_id: UUID
    column_ids: List[UUID]
    access_type: Optional[AccessType] = None

class CollectionResponseId(BaseModel):
    id: UUID

class CollectionUpdateParams(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    data_source_id: Optional[UUID] = None
    source_table_id: Optional[UUID] = None
    add_column_ids: Optional[List[UUID]] = None
    delete_column_ids: Optional[List[UUID]] = None
    access_type: Optional[AccessType] = None

class CollectionUserInvitationParams(BaseModel):
    emails: List[str]

class CollectionUserInvitationResponse(BaseModel):
    email: str
    status: InvitationStatus
    user_id: Optional[UUID] = None

class CreditsResponse(BaseModel):
    total_user_credits: float
    is_eligable_for_free_credit: bool

class CurrentResponse(BaseModel):
    pid: str
    name: str
    email: str
    credits: float
    is_admin: bool

class DataSourceParams(BaseModel):
    name: str
    provider: Provider
    username: str
    password: str
    uri: str
    id: Optional[UUID] = None

class DataSourceResponseId(BaseModel):
    id: UUID

class DataSourceResponseNoSchema(BaseModel):
    id: UUID
    name: str
    provider: Provider
    username: str
    uri: str
    is_valid: bool

class Filter(BaseModel):
    column: str
    comparison: CompareOp

class FilterWithColumnType(Filter):
    column_type: ColumnType

class InjectedDataParams(BaseModel):
    aggregation: AggregationWithColumnType
    filter: Optional[FilterWithColumnType] = None

class LLMConversationHistoryResponse(BaseModel):
    id: UUID
    created_at: str # Consider using datetime if parsing is consistent
    question: str
    answer: LLMAnswer
    proof: LLMProof
    is_proof_generating: bool
    is_loading: bool
    metadata: LLMMetadata
    source: Optional[LLMSource] = None

class LLMConversationParams(BaseModel):
    prompt: str
    collection_types: List[LLMCollectionType]
    is_proof_generated: bool
    is_web_search_enabled: bool

class LLMConversationVerifyResponse(BaseModel):
    proof_request_id: UUID

class ProofOnInjectedDataRequestParams(BaseModel):
    data: List[Dict[str, str]]
    query: InjectedDataParams

class ProofRequestParams(BaseModel):
    query: str
    collection_id: UUID
    id: Optional[UUID] = None

class ProofRequestResponseId(BaseModel):
    id: UUID
    llm_explanation: Optional[str] = None
    query_result: Optional[str] = None

class ProofVerificationParams(BaseModel):
    proof_bytes: list[int]

class ProofVerificationResponseId(BaseModel):
    id: UUID

class RequestCreditsParams(BaseModel):
    credit_amount: float
    is_user_feedback_positive: Optional[bool] = None

class RequestCreditsResponse(BaseModel):
    is_approved: bool
    total_user_credits: float

class RunQueryParams(BaseModel):
    query: str
    collection_id: UUID

class RunQueryResponse(BaseModel):
    query_result: Optional[str] = None

class SourceTableColumnParams(BaseModel):
    name: str
    type: ColumnType
    id: Optional[UUID] = None

class SourceTableColumnResponse(BaseModel):
    id: UUID
    name: str
    data_type: ColumnType
    invalid_field: Optional[SourceTableColumnInvalidFieldType] = None

class SourceTableCreateParams(BaseModel):
    name: str
    data_source_id: UUID
    schema_name: str
    table_name: str
    columns: List[SourceTableColumnParams]

class SourceTableResponse(BaseModel):
    name: str
    id: UUID
    schema_name: str
    table_name: str
    is_valid: bool


class SourceTableResponseId(BaseModel):
    id: UUID

class WithDataSourceId(BaseModel):
    data_source_id: UUID

class SourceTableResponseWithColumns(WithDataSourceId):
    columns: List[SourceTableColumnResponse]

class SourceTableResponseWithDataSourceId(SourceTableResponse):
    data_source_id: UUID

class SourceTableUpdateParams(BaseModel):
    name: Optional[str] = None
    data_source_id: Optional[UUID] = None
    schema_name: Optional[str] = None
    table_name: Optional[str] = None
    columns: Optional[List[SourceTableColumnParams]] = None
    delete_columns: Optional[List[UUID]] = None

class SourceTableValidationResponse(BaseModel):
    columns: List[SourceTableColumnResponse]
    is_valid: bool
    table_issue: Optional[SourceTableValidationIssue] = None

class StatsResponse(BaseModel):
    credits_trend_24h: float
    credits_earned: float
    query_count_trend_24h: float
    query_count: int
    total_user_credits: Optional[float] = None

class ValidateResponse(BaseModel):
    is_valid: bool
    error: Optional[str] = None

class ValidationErrorResponse(BaseModel):
    validation: SourceTableValidationResponse
    message: str


# Complex Response Schemas that reference other models
class DataSourceResponse(BaseModel):
    id: UUID
    name: str
    provider: Provider
    username: str
    uri: str
    source_tables: List[SourceTableResponse]
    is_valid: bool

class CollectionResponse(BaseModel):
    id: UUID
    name: str
    description: str
    data_source: DataSourceResponseNoSchema
    source_table: SourceTableResponse
    owner_user_id: UUID
    column_count: int
    access_type: AccessType
    can_edit: bool
    query_count: int
    row_count: int
    created_at: str 
    updated_at: str 
    credits_earned: Optional[float] = None
    invalid_dependency: Optional[InvalidDependency] = None

class ProofRequestResponse(BaseModel):
    id: UUID
    query: str
    created_at: str 
    status: str
    user_id: UUID
    collection_id: Optional[UUID] = None
    collection_name: Optional[str] = None
    data_sources_id: Optional[UUID] = None
    llm_explanation: Optional[str] = None
    proof_generated_at: Optional[str] = None 
    query_cost: Optional[float] = None
    query_result: Optional[str] = None
    
class ProofVerificationResponse(BaseModel):
    id: UUID
    status: str
    proof_bytes: Optional[bytes] = None
    proof_generated_at: Optional[str] = None 
    query: Optional[str] = None
    query_result: Optional[str] = None
    verification_result: Optional[bool] = None


# Request Models for Query and Path Parameters
class CollectionQueryParams(BaseModel):
    type: Optional[str] = None
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str] = None
    sort_by: Optional[CollectionSortBy] = None
    is_valid: Optional[bool] = None
    sort_order: Optional[SortOrder] = None
    data_sources: Optional[List[UUID]] = None
    source_tables: Optional[List[UUID]] = None
    access_types: Optional[List[AccessType]] = None

class OpenDeleteCollectionRequest(BaseModel):
    id: UUID

class OpenUpdateCollectionRequest(BaseModel):
    id: UUID

class OpenDeleteCollectionAccessRequest(BaseModel):
    id: UUID

class OpenUpdateCollectionAccessRequest(BaseModel):
    id: UUID


class CollectionColumnsListQueryParams(BaseModel):
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str]
    sort_by: Optional[CollectionColumnsSortBy]
    sort_order: Optional[SortOrder]
    type: list[ColumnType]

class CollectionUsersListQueryParams(BaseModel):
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str] = None
    sort_by: Optional[CollectionUsersSortBy] = None
    sort_order: Optional[SortOrder] = None

class ValidateQueryParams(BaseModel):
    statement: str
    data_source_id: UUID

```