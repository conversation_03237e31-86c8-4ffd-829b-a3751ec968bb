#!/usr/bin/env python3
"""
Database migration management script for PMCP.

This script provides convenient commands for managing database migrations
using Alembic with our SQLModel setup.
"""

import asyncio
import subprocess
import sys
from pathlib import Path

import click
from loguru import logger

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from database.session import db_async_engine  # noqa: E402


@click.group()
def cli():
    """Database migration management for PMCP."""
    pass


@cli.command()
@click.option("--message", "-m", required=True, help="Migration message")
@click.option(
    "--autogenerate/--no-autogenerate",
    default=True,
    help="Auto-generate migration from model changes",
)
def create(message: str, autogenerate: bool):
    """Create a new migration."""
    logger.info(f"Creating migration: {message}")

    cmd = ["alembic", "revision"]
    if autogenerate:
        cmd.append("--autogenerate")
    cmd.extend(["-m", message])

    try:
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        if result.returncode == 0:
            logger.success("Migration created successfully")
            print(result.stdout)
        else:
            logger.error(f"Failed to create migration: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error creating migration: {e}")
        sys.exit(1)


@cli.command()
@click.option(
    "--revision", "-r", default="head", help="Target revision (default: head)"
)
def upgrade(revision: str):
    """Upgrade database to a revision."""
    logger.info(f"Upgrading database to revision: {revision}")

    try:
        result = subprocess.run(
            ["alembic", "upgrade", revision],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
        )
        if result.returncode == 0:
            logger.success("Database upgraded successfully")
            print(result.stdout)
        else:
            logger.error(f"Failed to upgrade database: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error upgrading database: {e}")
        sys.exit(1)


@cli.command()
@click.option("--revision", "-r", required=True, help="Target revision")
def downgrade(revision: str):
    """Downgrade database to a revision."""
    logger.info(f"Downgrading database to revision: {revision}")

    try:
        result = subprocess.run(
            ["alembic", "downgrade", revision],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
        )
        if result.returncode == 0:
            logger.success("Database downgraded successfully")
            print(result.stdout)
        else:
            logger.error(f"Failed to downgrade database: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error downgrading database: {e}")
        sys.exit(1)


@cli.command()
@click.option("--verbose", "-v", is_flag=True, help="Show verbose output")
def current(verbose: bool):
    """Show current database revision."""
    cmd = ["alembic", "current"]
    if verbose:
        cmd.append("--verbose")

    try:
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
        else:
            logger.error(f"Failed to get current revision: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error getting current revision: {e}")
        sys.exit(1)


@cli.command()
@click.option("--verbose", "-v", is_flag=True, help="Show verbose output")
def history(verbose: bool):
    """Show migration history."""
    cmd = ["alembic", "history"]
    if verbose:
        cmd.append("--verbose")

    try:
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
        else:
            logger.error(f"Failed to get migration history: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error getting migration history: {e}")
        sys.exit(1)


@cli.command()
def status():
    """Show database and migration status."""
    logger.info("Checking database connection and migration status...")

    async def check_status():
        try:
            # Test database connection
            async with db_async_engine.connect() as conn:
                logger.success("✓ Database connection successful")

            # Check current migration status
            result = subprocess.run(
                ["alembic", "current"], cwd=PROJECT_ROOT, capture_output=True, text=True
            )

            if result.returncode == 0:
                current_rev = result.stdout.strip()
                if current_rev:
                    logger.info(f"Current migration: {current_rev}")
                else:
                    logger.warning("No migrations have been applied")
            else:
                logger.error("Failed to get migration status")

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            sys.exit(1)

    asyncio.run(check_status())


@cli.command()
def init_db():
    """Initialize database with current schema (for fresh setups)."""
    logger.info("Initializing database with current schema...")

    try:
        # First, upgrade to head to apply all migrations
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            logger.success("Database initialized successfully")
            print(result.stdout)
        else:
            logger.error(f"Failed to initialize database: {result.stderr}")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        sys.exit(1)


if __name__ == "__main__":
    cli()
