# Provably API Integration

This document describes the integration of Provably API endpoints with the PMCP Action Server.

## Overview

The PMCP Action Server now provides MCP tools that wrap Provably API endpoints, allowing AI agents to interact with Provably services through the trusted PMCP gateway. All interactions are logged and auditable.

## Architecture

### Components

1. **HTTP Client** (`action_server/services/provably_client.py`)
   - Async HTTP client with authentication
   - Error handling and mapping to PMCP exceptions
   - Request/response logging

2. **Contracts** (`action_server/contracts/provably/`)
   - Pydantic models for request/response validation
   - Modular structure mirroring tool organization

3. **Tools** (`action_server/tools/provably/`)
   - MCP tools wrapping each API endpoint
   - Type-safe request/response handling

### Authentication

- API key verification against `PROVABLY_API_KEY` environment variable
- API key extracted from request headers via middleware (not from request body)
- Invalid API keys result in `PMCPPermissionError`

## Collections API

The Collections API provides 13 endpoints for managing Provably collections:

### Available Tools

1. **provably_list_collections** - List collections with filtering/pagination
2. **provably_create_collection** - Create a new collection
3. **provably_bulk_delete_collections** - Bulk delete collections
4. **provably_bulk_duplicate_collections** - Bulk duplicate collections
5. **provably_get_collection_by_id** - Get collection by ID
6. **provably_delete_collection** - Delete collection by ID
7. **provably_update_collection** - Update collection by ID
8. **provably_delete_collection_access** - Delete collection access
9. **provably_update_collection_access** - Update collection access
10. **provably_get_collection_columns** - Get collection columns
11. **provably_get_collection_users** - Get collection users
12. **provably_invite_users_to_collection** - Invite users to collection
13. **provably_delete_collection_invitation** - Delete user invitation

### Example Usage

```python
from action_server.contracts.provably.collections import CollectionListRequest, CollectionQueryParams

# List collections (API key comes from headers via middleware)
request = CollectionListRequest(
    query_params=CollectionQueryParams(
        page=0,
        page_size=10,
        query="search_term"
    )
)

# Call through MCP client (API key should be in headers)
result = await client.call_tool("provably_list_collections", request.model_dump())
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Provably API Configuration
PROVABLY_API_KEY=your_provably_api_key_here
PROVABLY_BASE_URL=https://api.provably.ai
```

### Required Dependencies

All dependencies are already included in the project:
- `httpx` - HTTP client (via FastMCP)
- `pydantic` - Data validation
- `loguru` - Logging

### Middleware Implementation Required

**Important**: The API key extraction middleware is not yet implemented. You need to:

1. **Implement API Key Middleware** - Complete the implementation in `action_server/middlewares/api_key_middleware.py`
2. **Extract from Headers** - Extract API key from request headers (e.g., `X-API-Key`)
3. **Store in Context** - Make the API key available to tools through the context
4. **Register Middleware** - Add the middleware to the MCP server

See the template in `action_server/middlewares/api_key_middleware.py` for guidance.

## Error Handling

The integration maps Provably API errors to PMCP exceptions:

- **401 Unauthorized** → `PMCPPermissionError`
- **403 Forbidden** → `PMCPPermissionError`
- **404 Not Found** → `ResourceUnavailableError`
- **400/422 Validation** → `InvalidParamsError`
- **500+ Server Errors** → `ResourceUnavailableError`
- **Network/Timeout** → `ResourceUnavailableError`

## Security Features

1. **API Key Verification** - All requests verify API key before making external calls
2. **Error Sanitization** - Internal errors are mapped to generic PMCP errors
3. **Request Logging** - All requests/responses logged through PMCP middleware
4. **Input Validation** - All inputs validated through Pydantic models

## Testing

Run the example test:

```bash
# Start PMCP Action Server
uv run python -m action_server

# In another terminal, run the test
uv run python examples/test_provably_collections.py
```

## Extending the Integration

To add more Provably API endpoints:

1. **Add Models** - Create contracts in `action_server/contracts/provably/`
2. **Create Tools** - Implement tools in `action_server/tools/provably/`
3. **Register Tools** - Add to `action_server/__main__.py`
4. **Update Imports** - Add to `action_server/tools/__init__.py`

## Logging and Auditability

All Provably API interactions are logged through the PMCP middleware system:

- Request details (method, endpoint, parameters)
- Response data and status codes
- Agent identity and session tracking
- Performance metrics (response times)
- Error details for debugging

This ensures full auditability of all AI agent interactions with Provably services.
