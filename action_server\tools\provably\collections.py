from fastmcp import Context
from fastmcp.tools import Tool

from action_server.core.config import Config
from action_server.services.provably_client import ProvablyHTTPClientV1
from action_server.contracts.provably.collections import (
    CollectionQueryParams,
    CollectionCreateRequest,
    CollectionBulkDeleteRequest,
    CollectionBulkDuplicateRequest,
    CollectionGetByIdRequest,
    CollectionDeleteRequest,
    CollectionUpdateRequest,
    CollectionAccessDeleteRequest,
    CollectionAccessUpdateRequest,
    CollectionColumnsGetRequest,
    CollectionUsersGetRequest,
    CollectionUsersInviteRequest,
    CollectionInvitationDeleteRequest,
    CollectionResponse,
    CollectionResponseId,
    CollectionBulkDeleteResponse,
    CollectionBulkDuplicateResponse,
    CollectionColumnsListResponse,
    CollectionUserInvitationResponse,
)


# Initialize HTTP client
client = ProvablyHTTPClientV1()


def _get_api_key_from_context(ctx: Context) -> str:
    """Extract API key from request context/headers."""
    return Config.PROVABLY_API_KEY.get_secret_value()


async def list_collections(query_params: CollectionQueryParams, ctx: Context) -> list[CollectionResponse]:
    """List collections with optional filtering and pagination."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.get(
        endpoint="/collections",
        api_key=api_key,
        params=query_params.model_dump(exclude_none=True)
    )
    return [CollectionResponse(**item) for item in response]


async def create_collection(request: CollectionCreateRequest, ctx: Context) -> CollectionResponseId:
    """Create a new collection."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.post(
        endpoint="/collections",
        api_key=api_key,
        data=request.collection_data.model_dump(exclude_none=True)
    )
    return CollectionResponseId(**response)


async def bulk_delete_collections(request: CollectionBulkDeleteRequest, ctx: Context) -> CollectionBulkDeleteResponse:
    """Bulk delete collections."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.post(
        endpoint="/api/v1/open/collections/bulk_delete",
        api_key=api_key,
        data=request.bulk_delete_data.model_dump(exclude_none=True)
    )
    return CollectionBulkDeleteResponse(**response)


async def bulk_duplicate_collections(request: CollectionBulkDuplicateRequest, ctx: Context) -> CollectionBulkDuplicateResponse:
    """Bulk duplicate collections."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.post(
        endpoint="/api/v1/open/collections/bulk_duplicate",
        api_key=api_key,
        data=request.bulk_duplicate_data.model_dump(exclude_none=True)
    )
    return CollectionBulkDuplicateResponse(**response)


async def get_collection_by_id(request: CollectionGetByIdRequest, ctx: Context) -> CollectionResponse:
    """Get a collection by its ID."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.get(
        endpoint=f"/api/v1/open/collections/{request.collection_id}",
        api_key=api_key
    )
    return CollectionResponse(**response)


async def delete_collection(request: CollectionDeleteRequest, ctx: Context) -> None:
    """Delete a collection by its ID."""
    api_key = _get_api_key_from_context(ctx)
    await client.delete(
        endpoint=f"/api/v1/open/collections/{request.collection_id}",
        api_key=api_key
    )
    return None


async def update_collection(request: CollectionUpdateRequest, ctx: Context) -> CollectionResponseId:
    """Update an existing collection by its ID."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.patch(
        endpoint=f"/api/v1/open/collections/{request.collection_id}",
        api_key=api_key,
        data=request.update_data.model_dump(exclude_none=True)
    )
    return CollectionResponseId(**response)


async def delete_collection_access(request: CollectionAccessDeleteRequest, ctx: Context) -> None:
    """Delete collection access."""
    api_key = _get_api_key_from_context(ctx)
    await client.delete(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/access",
        api_key=api_key
    )
    return None


async def update_collection_access(request: CollectionAccessUpdateRequest, ctx: Context) -> CollectionResponseId:
    """Update collection access."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.patch(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/access",
        api_key=api_key,
        data=request.access_data.model_dump(exclude_none=True)
    )
    return CollectionResponseId(**response)


async def get_collection_columns(request: CollectionColumnsGetRequest, ctx: Context) -> list[CollectionColumnsListResponse]:
    """Get columns for a specific collection."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.get(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/columns",
        api_key=api_key,
        params=request.query_params.model_dump(exclude_none=True)
    )
    return [CollectionColumnsListResponse(**item) for item in response]


async def get_collection_users(request: CollectionUsersGetRequest, ctx: Context) -> list[CollectionUserInvitationResponse]:
    """Get users associated with a collection."""
    api_key = _get_api_key_from_context(ctx)
    response = await client.get(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/users",
        api_key=api_key,
        params=request.query_params.model_dump(exclude_none=True)
    )
    return [CollectionUserInvitationResponse(**item) for item in response]


async def invite_users_to_collection(request: CollectionUsersInviteRequest, ctx: Context) -> None:
    """Invite users to a collection."""
    api_key = _get_api_key_from_context(ctx)
    await client.post(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/users",
        api_key=api_key,
        data=request.invitation_data.model_dump(exclude_none=True)
    )
    return None


async def delete_collection_invitation(request: CollectionInvitationDeleteRequest, ctx: Context) -> None:
    """Delete a user invitation from a collection."""
    api_key = _get_api_key_from_context(ctx)
    await client.delete(
        endpoint=f"/api/v1/open/collections/{request.collection_id}/users",
        api_key=api_key,
        # Note: DELETE with body - may need to adjust based on actual API behavior
    )
    return None


# Create MCP Tools
list_collections_tool = Tool.from_function(
    fn=list_collections,
    name="provably_list_collections",
    description="List Provably collections with optional filtering and pagination",
    enabled=True,
)

create_collection_tool = Tool.from_function(
    fn=create_collection,
    name="provably_create_collection",
    description="Create a new Provably collection",
    enabled=True,
)

bulk_delete_collections_tool = Tool.from_function(
    fn=bulk_delete_collections,
    name="provably_bulk_delete_collections",
    description="Bulk delete Provably collections",
    enabled=True,
)

bulk_duplicate_collections_tool = Tool.from_function(
    fn=bulk_duplicate_collections,
    name="provably_bulk_duplicate_collections",
    description="Bulk duplicate Provably collections",
    enabled=True,
)

get_collection_by_id_tool = Tool.from_function(
    fn=get_collection_by_id,
    name="provably_get_collection_by_id",
    description="Get a Provably collection by its ID",
    enabled=True,
)

delete_collection_tool = Tool.from_function(
    fn=delete_collection,
    name="provably_delete_collection",
    description="Delete a Provably collection by its ID",
    enabled=True,
)

update_collection_tool = Tool.from_function(
    fn=update_collection,
    name="provably_update_collection",
    description="Update an existing Provably collection by its ID",
    enabled=True,
)

delete_collection_access_tool = Tool.from_function(
    fn=delete_collection_access,
    name="provably_delete_collection_access",
    description="Delete Provably collection access",
    enabled=True,
)

update_collection_access_tool = Tool.from_function(
    fn=update_collection_access,
    name="provably_update_collection_access",
    description="Update Provably collection access",
    enabled=True,
)

get_collection_columns_tool = Tool.from_function(
    fn=get_collection_columns,
    name="provably_get_collection_columns",
    description="Get columns for a specific Provably collection",
    enabled=True,
)

get_collection_users_tool = Tool.from_function(
    fn=get_collection_users,
    name="provably_get_collection_users",
    description="Get users associated with a Provably collection",
    enabled=True,
)

invite_users_to_collection_tool = Tool.from_function(
    fn=invite_users_to_collection,
    name="provably_invite_users_to_collection",
    description="Invite users to a Provably collection",
    enabled=True,
)

delete_collection_invitation_tool = Tool.from_function(
    fn=delete_collection_invitation,
    name="provably_delete_collection_invitation",
    description="Delete a user invitation from a Provably collection",
    enabled=True,
)
