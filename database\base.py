from sqlmodel import MetaData, SQLModel

POSTGRES_INDEXES_NAMING_CONVENTION = {
    "ix": "%(column_0_label)s_idx",
    "uq": "%(table_name)s_%(column_0_name)s_key",
    "ck": "%(table_name)s_%(constraint_name)s_check",
    "fk": "%(table_name)s_%(column_0_name)s_fkey",
    "pk": "%(table_name)s_pkey",
}

db_metadata = MetaData(naming_convention=POSTGRES_INDEXES_NAMING_CONVENTION)

class BaseModel(SQLModel):
    metadata = db_metadata