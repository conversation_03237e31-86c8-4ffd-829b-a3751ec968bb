from loguru import logger
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError
from sqlalchemy.ext.asyncio import create_async_engine

from action_server.core.config import Config


async def _execute_database_check(conn, db_name) -> bool:
    """Execute database existence check query."""
    result = await conn.execute(
        text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
        {"db_name": db_name},
    )
    return result.scalar_one_or_none() is not None


async def _check_database_exists() -> bool:
    """Check if the database exists and return boolean result."""
    logger.info(f"Ensuring the database '{Config.DB_URL.database}' exists...")
    engine = create_async_engine(Config.DB_URL, isolation_level="AUTOCOMMIT")
    db_name = Config.DB_URL.database

    async with engine.connect() as conn:
        try:
            return await _execute_database_check(conn, db_name)
        except Exception as e:
            logger.error(f"Unexpected error checking database '{db_name}': {e}")
            raise


async def _initialize_database_if_not_exist():
    """Initialize database if it doesn't exist."""
    engine = create_async_engine(Config.DB_URL, isolation_level="AUTOCOMMIT")
    db_name = Config.DB_URL.database

    async with engine.connect() as conn:
        try:
            db_exists = await _execute_database_check(conn, db_name)

            if not db_exists:
                logger.info(
                    f"Database '{db_name}' does not exist. Attempting to create."
                )
                await conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                logger.info(f"Database '{db_name}' created successfully.")

        except ProgrammingError as e:
            # PostgreSQL error code for "duplicate_database" is 42P04
            if "42P04" in str(e) or "already exists" in str(e).lower():
                logger.warning(
                    "Database effectively exists (creation attempt failed as it already exists)."
                )
            else:
                logger.error(
                    f"Programming error during database check/creation for '{db_name}': {e}"
                )
                raise
        except Exception as e:
            logger.error(f"Unexpected error ensuring database '{db_name}' exists: {e}")
            raise


async def run_migrations() -> None:
    """Run Alembic migrations to upgrade database to latest version."""
    import subprocess
    from pathlib import Path

    project_root = Path(__file__).parent.parent
    logger.info("Running Alembic migrations...")

    try:
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=project_root,
            capture_output=True,
            text=True,
            check=True,
        )
        logger.success("✓ Database migrations completed successfully")
        if result.stdout.strip():
            logger.debug(f"Migration output: {result.stdout}")
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ Migration failed: {e.stderr}")
        raise
    except Exception as e:
        logger.error(f"✗ Error running migrations: {e}")
        raise


async def init_db() -> None:
    """Initialize database based on environment and configuration."""
    if not Config.IS_PROD_LIKE:
        logger.info(f"Database initialization in {Config.ENVIRONMENT} environment.")
        await _initialize_database_if_not_exist()
        await run_migrations()
    else:
        logger.info("Using Alembic migrations for schema management")
        db_exists = await _check_database_exists()
        if not db_exists:
            raise RuntimeError(
                f"Database '{Config.DB_URL.database}' does not exist in production environment"
            )
        await run_migrations()
    logger.success("✓ Database initialization completed successfully")
