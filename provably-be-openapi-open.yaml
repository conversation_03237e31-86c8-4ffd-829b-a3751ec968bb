openapi: 3.0.3
info:
  title: Provably API docs
  description: ''
  license:
    name: ''
  version: 1.6.0
paths:
  /api/v1/open/collections/:
    get:
      tags:
        - Collection
      operationId: open_list_collections
      parameters:
        - name: type
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: query
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sort_by
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/CollectionSortBy'
            nullable: true
        - name: is_valid
          in: query
          required: false
          schema:
            type: boolean
            nullable: true
        - name: sort_order
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/SortOrder'
            nullable: true
        - name: data_sources
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              format: uuid
        - name: source_tables
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              format: uuid
        - name: access_types
          in: query
          required: false
          schema:
            type: array
            items:
              $ref: '#/components/schemas/AccessType'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CollectionResponse'
      security:
        - API_KEY: []
    post:
      tags:
        - Collection
      operationId: open_create_collection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionCreateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionResponseId'
      security:
        - API_KEY: []
  /api/v1/open/collections/bulk_delete:
    post:
      tags:
        - Collection
      operationId: open_bulk_delete_collections
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionBulkDeleteParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionBulkDeleteResponse'
      security:
        - API_KEY: []
  /api/v1/open/collections/bulk_duplicate:
    post:
      tags:
        - Collection
      operationId: open_bulk_duplicate_collections
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionBulkDuplicateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionBulkDuplicateResponse'
      security:
        - API_KEY: []
  /api/v1/open/collections/{id}:
    get:
      tags:
        - Collection
      operationId: open_get_collection_by_id
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionResponse'
      security:
        - API_KEY: []
    delete:
      tags:
        - Collection
      operationId: open_delete_collection
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
      security:
        - API_KEY: []
    patch:
      tags:
        - Collection
      operationId: open_update_collection
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionUpdateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionResponseId'
      security:
        - API_KEY: []
  /api/v1/open/collections/{id}/access:
    delete:
      tags:
        - Collection
      operationId: open_delete_collection_access
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                default: null
                nullable: true
      security:
        - API_KEY: []
    patch:
      tags:
        - Collection
      operationId: open_update_collection_access
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionAccessUpdateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionResponseId'
      security:
        - API_KEY: []
  /api/v1/open/collections/{id}/columns:
    get:
      tags:
        - Collection
      operationId: open_get_collection_columns
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: query
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sort_by
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/CollectionColumnsSortBy'
            nullable: true
        - name: sort_order
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/SortOrder'
            nullable: true
        - name: type
          in: query
          required: false
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ColumnType'
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CollectionColumnsListResponse'
      security:
        - API_KEY: []
  /api/v1/open/collections/{id}/users:
    get:
      tags:
        - Collection
      operationId: open_get_collection_users
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            format: int64
            nullable: true
            minimum: 0
        - name: query
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sort_by
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/CollectionUsersSortBy'
            nullable: true
        - name: sort_order
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/SortOrder'
            nullable: true
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CollectionUserInvitationResponse'
      security:
        - API_KEY: []
    post:
      tags:
        - Collection
      operationId: open_invite_users_to_collection
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionUserInvitationParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                default: null
                nullable: true
      security:
        - API_KEY: []
    delete:
      tags:
        - Collection
      operationId: open_delete_collection_invitation
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionUserInvitationParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                default: null
                nullable: true
      security:
        - API_KEY: []
  /api/v1/open/conversation/:
    post:
      tags:
        - Chatbot
      summary: Chatbot conversation
      description: Chatbot conversation
      operationId: conversion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLMConversationParams'
        required: true
      responses:
        '200':
          description: ''
      security:
        - JWT: []
  /api/v1/open/conversation/history:
    get:
      tags:
        - Chatbot
      summary: Chatbot conversation history
      description: Chatbot conversation history
      operationId: openConversionHistory
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LLMConversationHistoryResponse'
      security:
        - API_KEY: []
  /api/v1/open/conversation/{id}/verify:
    post:
      tags:
        - Chatbot
      summary: Verify conversation
      description: Verify conversation
      operationId: openVerifyConversation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LLMConversationVerifyResponse'
      security:
        - API_KEY: []
  /api/v1/open/credits/:
    get:
      tags:
        - Credits
      summary: Get user credit info
      description: Get user credit info before given date (at_date format - 2024-09-29T00:00:00)
      operationId: openGetUserCreditInfo
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditsResponse'
        '400':
          description: ''
      security:
        - API_KEY: []
  /api/v1/open/credits/request:
    post:
      tags:
        - Credits
      summary: Request credits
      description: Request credits (1 credit per 24h is automatically added if the user requests it, otherwise the credit request is just stored in database for later approval)
      operationId: openRequestCredits
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestCreditsParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreditsResponse'
        '400':
          description: ''
      security:
        - API_KEY: []
  /api/v1/open/data_sources/:
    get:
      tags:
        - Data source
      operationId: open_list_data_source
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DataSourceResponse'
      security:
        - API_KEY: []
    post:
      tags:
        - Data source
      summary: Create a new data source
      description: Return bad request if cannot connect to datasource credentials
      operationId: open_create_data_source
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataSourceParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataSourceResponse'
      security:
        - API_KEY: []
  /api/v1/open/data_sources/{id}:
    get:
      tags:
        - Data source
      operationId: open_get_by_id_data_source
      parameters:
        - name: id
          in: path
          description: Data source id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataSourceResponse'
      security:
        - API_KEY: []
    delete:
      tags:
        - Data source
      operationId: open_remove_data_source
      parameters:
        - name: id
          in: path
          description: Data source database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataSourceResponseId'
      security:
        - API_KEY: []
    patch:
      tags:
        - Data source
      operationId: open_update_data_source
      parameters:
        - name: id
          in: path
          description: Data source id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataSourceParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataSourceResponseId'
      security:
        - API_KEY: []
  /api/v1/open/data_sources/{id}/connect:
    get:
      tags:
        - Data source
      description: Test connection to data source (updates is_valid and returns the data source)
      operationId: open_test_connection_data_source
      parameters:
        - name: id
          in: path
          description: Data source database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataSourceResponseNoSchema'
      security:
        - API_KEY: []
  /api/v1/open/proof_requests/:
    get:
      tags:
        - Proof request
      operationId: open_list_proof_request
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/ProofRequestResponse'
      security:
        - API_KEY: []
    post:
      tags:
        - Proof request
      operationId: open_create_proof_request
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProofRequestParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofRequestResponseId'
      security:
        - API_KEY: []
  /api/v1/open/proof_requests/inject_data:
    post:
      tags:
        - Injected data request
      operationId: open_inject_data
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProofOnInjectedDataRequestParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofRequestResponseId'
      security:
        - API_KEY: []
  /api/v1/open/proof_requests/{id}:
    get:
      tags:
        - Proof request
      operationId: open_get_by_id_proof_request
      parameters:
        - name: id
          in: path
          description: Proof request database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofRequestResponse'
      security:
        - API_KEY: []
    put:
      tags:
        - Proof request
      operationId: open_cancel_proof_request
      parameters:
        - name: id
          in: path
          description: Proof request id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofRequestResponseId'
      security:
        - API_KEY: []
  /api/v1/open/proof_requests/{id}/download:
    get:
      tags:
        - Proof request
      operationId: open_download_proof_bytes
      parameters:
        - name: id
          in: path
          description: Proof request database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Returns proof bytes as a text file
      security:
        - API_KEY: []
  /api/v1/open/proof_verifications/:
    get:
      tags:
        - Proof verification
      operationId: open_list_proof_verification
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/ProofVerificationResponse'
      security:
        - API_KEY: []
    post:
      tags:
        - Proof verification
      operationId: open_create_proof_verification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProofVerificationParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofVerificationResponseId'
      security:
        - API_KEY: []
  /api/v1/open/proof_verifications/{id}:
    get:
      tags:
        - Proof verification
      operationId: open_get_by_id_proof_verification
      parameters:
        - name: id
          in: path
          description: Proof verification database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofVerificationResponse'
      security:
        - API_KEY: []
  /api/v1/open/proof_verifications/{id}/cancel:
    put:
      tags:
        - Proof verification
      operationId: open_cancel_proof_verification
      parameters:
        - name: id
          in: path
          description: Proof verification id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProofVerificationResponseId'
      security:
        - API_KEY: []
  /api/v1/open/query/:
    get:
      tags:
        - Query
      operationId: open_validate
      parameters:
        - name: statement
          in: query
          required: true
          schema:
            type: string
        - name: data_source_id
          in: query
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ValidateResponse'
      security:
        - API_KEY: []
  /api/v1/open/source_tables/:
    post:
      tags:
        - Source table
      operationId: open_create
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SourceTableCreateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceTableResponseId'
        '400':
          description: Source table validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
      security:
        - API_KEY: []
  /api/v1/open/source_tables/data_source/{id}:
    get:
      tags:
        - Source table
      operationId: open_list_by_data_source_id
      parameters:
        - name: id
          in: path
          description: Data source database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SourceTableResponse'
        '404':
          description: Data source not found
      security:
        - API_KEY: []
  /api/v1/open/source_tables/{id}:
    get:
      tags:
        - Source table
      operationId: open_get_by_id
      parameters:
        - name: id
          in: path
          description: Source table database id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceTableResponse'
        '404':
          description: Source table not found
      security:
        - API_KEY: []
    delete:
      tags:
        - Source table
      operationId: open_remove
      parameters:
        - name: id
          in: path
          description: Source Table Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceTableResponseId'
        '404':
          description: Source table not found
      security:
        - API_KEY: []
    patch:
      tags:
        - Source table
      operationId: update_source_table
      parameters:
        - name: id
          in: path
          description: Source table database id
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SourceTableUpdateParams'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceTableResponse'
        '400':
          description: Source table validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
      security:
        - API_KEY: []
  /api/v1/open/source_tables/{id}/connect:
    get:
      tags:
        - Source table
      summary: Check connection of source table
      description: Check the connection of the source table and update the is_valid field of the source table and its columns
      operationId: openCheckConnectionSourceTable
      parameters:
        - name: id
          in: path
          description: Source Table Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceTableResponse'
        '400':
          description: Source table validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
      security:
        - API_KEY: []
  /api/v1/open/sse_stream/:
    get:
      tags:
        - Server-Sent Events API
      summary: SSE Stream
      description: Server-Sent Events stream
      operationId: openSseStream
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: string
      security:
        - API_KEY: []
  /api/v1/open/stats/:
    get:
      tags:
        - Statistics
      summary: Get total statistics
      description: Get statistics for the current user
      operationId: openGetTotalStats
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatsResponse'
        '400':
          description: ''
      security:
        - API_KEY: []
  /api/v1/open/stats/collection/{id}:
    get:
      tags:
        - Statistics
      summary: Get collection statistics
      description: Get statistics for a specific collection (error if the collection is not owned by the user)
      operationId: openGetCollectionStats
      parameters:
        - name: id
          in: path
          description: Collection Id
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatsResponse'
        '400':
          description: ''
      security:
        - API_KEY: []
  /api/v1/open/user/current:
    get:
      tags:
        - User
      operationId: open_current
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CurrentResponse'
      security:
        - API_KEY: []
  /api/v1/open/user/key:
    get:
      tags:
        - User
      operationId: open_key
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApiKeyResponse'
      security:
        - API_KEY: []
components:
  schemas:
    AccessType:
      type: string
      enum:
        - draft
        - public
        - limited_access
    Aggregation:
      type: object
      description: |-
        An aggregation that's computed over the values of a column.

        Represents an occurrence of an aggregation such as `function(column)`
        within the `SELECT` clause of a query.
      required:
        - function
        - column
      properties:
        alias:
          type: string
          description: 'The alias that''s assigned to the result of the function: `function(column) AS alias`.'
          nullable: true
        column:
          type: string
          description: The name of the column on which the function is executed.
        function:
          $ref: '#/components/schemas/KoronFunction'
    AggregationWithColumnType:
      allOf:
        - $ref: '#/components/schemas/Aggregation'
        - type: object
          required:
            - column_type
          properties:
            column_type:
              $ref: '#/components/schemas/ColumnType'
    ApiKeyResponse:
      type: object
      required:
        - api_key
      properties:
        api_key:
          type: string
    CollectionAccessUpdateParams:
      type: object
      required:
        - access_type
      properties:
        access_type:
          $ref: '#/components/schemas/AccessType'
    CollectionBulkDeleteParams:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: string
            format: uuid
    CollectionBulkDeleteResponse:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: string
            format: uuid
    CollectionBulkDuplicateParams:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: string
            format: uuid
    CollectionBulkDuplicateResponse:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: string
            format: uuid
    CollectionColumnResponse:
      type: object
      required:
        - id
        - name
        - type
        - description
      properties:
        description:
          type: string
        id:
          type: string
          format: uuid
        invalid_field:
          allOf:
            - $ref: '#/components/schemas/SourceTableColumnInvalidFieldType'
          nullable: true
        name:
          type: string
        type:
          $ref: '#/components/schemas/ColumnType'
    CollectionColumnsListQueryParams:
      type: object
      properties:
        page:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        page_size:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        query:
          type: string
          nullable: true
        sort_by:
          allOf:
            - $ref: '#/components/schemas/CollectionColumnsSortBy'
          nullable: true
        sort_order:
          allOf:
            - $ref: '#/components/schemas/SortOrder'
          nullable: true
        type:
          type: array
          items:
            $ref: '#/components/schemas/ColumnType'
    CollectionColumnsListResponse:
      type: object
      required:
        - columns
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/SourceTableColumnResponse'
    CollectionCreateParams:
      type: object
      required:
        - name
        - description
        - data_source_id
        - source_table_id
        - column_ids
      properties:
        access_type:
          allOf:
            - $ref: '#/components/schemas/AccessType'
          nullable: true
        column_ids:
          type: array
          items:
            type: string
            format: uuid
        data_source_id:
          type: string
          format: uuid
        description:
          type: string
        name:
          type: string
        source_table_id:
          type: string
          format: uuid
    CollectionQueryParams:
      type: object
      properties:
        access_types:
          type: array
          items:
            $ref: '#/components/schemas/AccessType'
        data_sources:
          type: array
          items:
            type: string
            format: uuid
        is_valid:
          type: boolean
          nullable: true
        page:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        page_size:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        query:
          type: string
          nullable: true
        sort_by:
          allOf:
            - $ref: '#/components/schemas/CollectionSortBy'
          nullable: true
        sort_order:
          allOf:
            - $ref: '#/components/schemas/SortOrder'
          nullable: true
        source_tables:
          type: array
          items:
            type: string
            format: uuid
        type:
          type: string
          nullable: true
    CollectionResponse:
      type: object
      required:
        - id
        - name
        - description
        - data_source
        - source_table
        - owner_user_id
        - column_count
        - access_type
        - can_edit
        - query_count
        - row_count
        - created_at
        - updated_at
      properties:
        access_type:
          $ref: '#/components/schemas/AccessType'
        can_edit:
          type: boolean
        column_count:
          type: integer
          format: int64
          minimum: 0
        created_at:
          type: string
        credits_earned:
          type: number
          format: double
          nullable: true
        data_source:
          $ref: '#/components/schemas/DataSourceResponseNoSchema'
        description:
          type: string
        id:
          type: string
          format: uuid
        invalid_dependency:
          allOf:
            - $ref: '#/components/schemas/InvalidDependency'
          nullable: true
        name:
          type: string
        owner_user_id:
          type: string
          format: uuid
        query_count:
          type: integer
          format: int64
          minimum: 0
        row_count:
          type: integer
          format: int64
          minimum: 0
        source_table:
          $ref: '#/components/schemas/SourceTableResponse'
        updated_at:
          type: string
    CollectionResponseId:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    CollectionUpdateParams:
      type: object
      properties:
        access_type:
          allOf:
            - $ref: '#/components/schemas/AccessType'
          nullable: true
        add_column_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
        data_source_id:
          type: string
          format: uuid
          nullable: true
        delete_column_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
        description:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        source_table_id:
          type: string
          format: uuid
          nullable: true
    CollectionUserInvitationParams:
      type: object
      required:
        - emails
      properties:
        emails:
          type: array
          items:
            type: string
    CollectionUserInvitationResponse:
      type: object
      required:
        - email
        - status
      properties:
        email:
          type: string
        status:
          $ref: '#/components/schemas/InvitationStatus'
        user_id:
          type: string
          format: uuid
          nullable: true
    CollectionUsersListQueryParams:
      type: object
      properties:
        page:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        page_size:
          type: integer
          format: int64
          nullable: true
          minimum: 0
        query:
          type: string
          nullable: true
        sort_by:
          allOf:
            - $ref: '#/components/schemas/CollectionUsersSortBy'
          nullable: true
        sort_order:
          allOf:
            - $ref: '#/components/schemas/SortOrder'
          nullable: true
    ColumnType:
      type: string
      enum:
        - integer
        - bigint
        - smallint
        - decimal
        - time
        - timestamp
        - date
        - boolean
        - uuid
        - string
    CompareOp:
      oneOf:
        - type: object
          description: Check if column's value is less than `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - Lt
            value:
              type: string
        - type: object
          description: Check if column's value is less than or equal to `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - LtEq
            value:
              type: string
        - type: object
          description: Check if column's value is greater than `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - Gt
            value:
              type: string
        - type: object
          description: Check if column's value is greater than or equal to `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - GtEq
            value:
              type: string
        - type: object
          description: Check if column's value is equal to `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - Eq
            value:
              type: string
        - type: object
          description: Check if column's value is not equal to `value`.
          required:
            - value
            - type
          properties:
            type:
              type: string
              enum:
                - NotEq
            value:
              type: string
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsNull
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsNotNull
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsTrue
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsNotTrue
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsFalse
        - type: object
          required:
            - type
          properties:
            type:
              type: string
              enum:
                - IsNotFalse
      description: The comparison operation between the value of an unspecified column and some constant values.
      discriminator:
        propertyName: type
    CreditsParams:
      type: object
      properties:
        at_date:
          allOf:
            - $ref: '#/components/schemas/NaiveDateTime'
          nullable: true
    CreditsResponse:
      type: object
      required:
        - total_user_credits
        - is_eligable_for_free_credit
      properties:
        is_eligable_for_free_credit:
          type: boolean
        total_user_credits:
          type: number
          format: double
    CurrentResponse:
      type: object
      required:
        - pid
        - name
        - email
        - credits
        - is_admin
      properties:
        credits:
          type: number
          format: double
        email:
          type: string
        is_admin:
          type: boolean
        name:
          type: string
        pid:
          type: string
    DataSourceParams:
      type: object
      required:
        - name
        - provider
        - username
        - password
        - uri
      properties:
        id:
          type: string
          format: uuid
          nullable: true
        name:
          type: string
        password:
          type: string
        provider:
          $ref: '#/components/schemas/Provider'
        uri:
          type: string
        username:
          type: string
    DataSourceResponse:
      type: object
      required:
        - id
        - name
        - provider
        - username
        - uri
        - source_tables
        - is_valid
      properties:
        id:
          type: string
          format: uuid
        is_valid:
          type: boolean
        name:
          type: string
        provider:
          $ref: '#/components/schemas/Provider'
        source_tables:
          type: array
          items:
            $ref: '#/components/schemas/SourceTableResponse'
        uri:
          type: string
        username:
          type: string
    DataSourceResponseId:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    DataSourceResponseNoSchema:
      type: object
      required:
        - id
        - name
        - provider
        - username
        - uri
        - is_valid
      properties:
        id:
          type: string
          format: uuid
        is_valid:
          type: boolean
        name:
          type: string
        provider:
          $ref: '#/components/schemas/Provider'
        uri:
          type: string
        username:
          type: string
    Filter:
      type: object
      description: Contains information related to the filter applied in the query parsed.
      required:
        - column
        - comparison
      properties:
        column:
          type: string
          description: Column on which the filter is applied.
        comparison:
          $ref: '#/components/schemas/CompareOp'
    FilterWithColumnType:
      allOf:
        - $ref: '#/components/schemas/Filter'
        - type: object
          required:
            - column_type
          properties:
            column_type:
              $ref: '#/components/schemas/ColumnType'
    InjectedDataParams:
      type: object
      required:
        - aggregation
      properties:
        aggregation:
          $ref: '#/components/schemas/AggregationWithColumnType'
        filter:
          allOf:
            - $ref: '#/components/schemas/FilterWithColumnType'
          nullable: true
    InvitationStatus:
      type: string
      enum:
        - joined
        - not_joined
    KoronFunction:
      type: string
      description: Represents a Koron aggregation / analytic function.
      enum:
        - Sum
        - Count
        - Average
        - Median
        - Variance
        - StandardDeviation
        - Min
        - Max
    LLMConversationHistoryResponse:
      type: object
      required:
        - id
        - created_at
        - question
        - answer
        - proof
        - is_proof_generating
        - is_loading
        - metadata
      properties:
        answer:
          $ref: '#/components/schemas/LLMAnswer'
        created_at:
          type: string
        id:
          type: string
          format: uuid
        is_loading:
          type: boolean
        is_proof_generating:
          type: boolean
        metadata:
          $ref: '#/components/schemas/LLMMetadata'
        proof:
          $ref: '#/components/schemas/LLMProof'
        question:
          type: string
        source:
          allOf:
            - $ref: '#/components/schemas/LLMSource'
          nullable: true
    LLMConversationParams:
      type: object
      required:
        - prompt
        - collection_types
        - is_proof_generated
        - is_web_search_enabled
      properties:
        collection_types:
          type: array
          items:
            $ref: '#/components/schemas/LLMCollectionType'
        is_proof_generated:
          type: boolean
        is_web_search_enabled:
          type: boolean
        prompt:
          type: string
    LLMConversationVerifyResponse:
      type: object
      required:
        - proof_request_id
      properties:
        proof_request_id:
          type: string
          format: uuid
    ProofOnInjectedDataRequestParams:
      type: object
      required:
        - data
        - query
      properties:
        data:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
        query:
          $ref: '#/components/schemas/InjectedDataParams'
    ProofRequestParams:
      type: object
      required:
        - query
        - collection_id
      properties:
        collection_id:
          type: string
          format: uuid
        id:
          type: string
          format: uuid
          nullable: true
        query:
          type: string
    ProofRequestResponse:
      type: object
      required:
        - id
        - query
        - created_at
        - status
        - user_id
      properties:
        collection_id:
          type: string
          format: uuid
          nullable: true
        collection_name:
          type: string
          nullable: true
        created_at:
          type: string
        data_sources_id:
          type: string
          format: uuid
          nullable: true
        id:
          type: string
          format: uuid
        llm_explanation:
          type: string
          nullable: true
        proof_generated_at:
          type: string
          nullable: true
        query:
          type: string
        query_cost:
          type: number
          format: double
          nullable: true
        query_result:
          type: string
          nullable: true
        status:
          type: string
        user_id:
          type: string
          format: uuid
    ProofRequestResponseId:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
        llm_explanation:
          type: string
          nullable: true
        query_result:
          type: string
          nullable: true
    ProofVerificationParams:
      type: object
      required:
        - proof_bytes
      properties:
        proof_bytes:
          type: string
          format: binary
    ProofVerificationResponse:
      type: object
      required:
        - id
        - status
      properties:
        id:
          type: string
          format: uuid
        proof_bytes:
          type: string
          format: binary
          nullable: true
        proof_generated_at:
          type: string
          nullable: true
        query:
          type: string
          nullable: true
        query_result:
          type: string
          nullable: true
        status:
          type: string
        verification_result:
          type: boolean
          nullable: true
    ProofVerificationResponseId:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    Provider:
      type: string
      enum:
        - postgresql
    RequestCreditsParams:
      type: object
      required:
        - credit_amount
      properties:
        credit_amount:
          type: number
          format: double
        is_user_feedback_positive:
          type: boolean
          nullable: true
    RequestCreditsResponse:
      type: object
      required:
        - is_approved
        - total_user_credits
      properties:
        is_approved:
          type: boolean
        total_user_credits:
          type: number
          format: double
    RunQueryParams:
      type: object
      required:
        - query
        - collection_id
      properties:
        collection_id:
          type: string
          format: uuid
        query:
          type: string
    RunQueryResponse:
      type: object
      properties:
        query_result:
          type: string
          nullable: true
    SourceTableColumnParams:
      type: object
      required:
        - name
        - type
      properties:
        id:
          type: string
          format: uuid
          nullable: true
        name:
          type: string
        type:
          $ref: '#/components/schemas/ColumnType'
    SourceTableColumnResponse:
      type: object
      required:
        - id
        - name
        - type
      properties:
        id:
          type: string
          format: uuid
        invalid_field:
          allOf:
            - $ref: '#/components/schemas/SourceTableColumnInvalidFieldType'
          nullable: true
        name:
          type: string
        type:
          $ref: '#/components/schemas/ColumnType'
    SourceTableCreateParams:
      type: object
      required:
        - name
        - data_source_id
        - schema_name
        - table_name
        - columns
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/SourceTableColumnParams'
        data_source_id:
          type: string
          format: uuid
        name:
          type: string
        schema_name:
          type: string
        table_name:
          type: string
    SourceTableResponse:
      type: object
      required:
        - name
        - id
        - schema_name
        - table_name
        - is_valid
      properties:
        id:
          type: string
          format: uuid
        is_valid:
          type: boolean
        name:
          type: string
        schema_name:
          type: string
        table_name:
          type: string
    SourceTableResponseId:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    SourceTableResponseWithColumns:
      allOf:
        - $ref: '#/components/schemas/WithDataSourceId'
        - type: object
          required:
            - columns
          properties:
            columns:
              type: array
              items:
                $ref: '#/components/schemas/SourceTableColumnResponse'
    SourceTableResponseWithDataSourceId:
      allOf:
        - $ref: '#/components/schemas/SourceTableResponse'
        - type: object
          required:
            - data_source_id
          properties:
            data_source_id:
              type: string
              format: uuid
    SourceTableUpdateParams:
      type: object
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/SourceTableColumnParams'
          nullable: true
        data_source_id:
          type: string
          format: uuid
          nullable: true
        delete_columns:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
        name:
          type: string
          nullable: true
        schema_name:
          type: string
          nullable: true
        table_name:
          type: string
          nullable: true
    SourceTableValidationIssue:
      type: string
      enum:
        - schema_not_found
        - table_not_found
        - connection_error
    SourceTableValidationResponse:
      type: object
      required:
        - columns
        - is_valid
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/SourceTableColumnResponse'
        is_valid:
          type: boolean
        table_issue:
          allOf:
            - $ref: '#/components/schemas/SourceTableValidationIssue'
          nullable: true
    StatsResponse:
      type: object
      required:
        - credits_trend_24h
        - credits_earned
        - query_count_trend_24h
        - query_count
      properties:
        credits_earned:
          type: number
          format: double
        credits_trend_24h:
          type: number
          format: double
        query_count:
          type: integer
          format: int64
          minimum: 0
        query_count_trend_24h:
          type: number
          format: double
        total_user_credits:
          type: number
          format: double
          nullable: true
    ValidateQueryParams:
      type: object
      required:
        - statement
        - data_source_id
      properties:
        data_source_id:
          type: string
          format: uuid
        statement:
          type: string
    ValidateResponse:
      type: object
      required:
        - is_valid
      properties:
        error:
          type: string
          nullable: true
        is_valid:
          type: boolean
    ValidationErrorResponse:
      type: object
      required:
        - validation
        - message
      properties:
        message:
          type: string
        validation:
          $ref: '#/components/schemas/SourceTableValidationResponse'
  securitySchemes:
    API_KEY:
      type: http
      scheme: bearer
tags:
  - name: Query
    description: Query API
  - name: Proof request
    description: Proof request management API
  - name: Proof verification
    description: Proof verification management API
  - name: Collection
    description: Collection management API
  - name: Credits
    description: Credits management API
  - name: Data source
    description: Data source management API
  - name: Source table
    description: Source table management API
  - name: User
    description: User management API
  - name: Statistics
    description: Statistics management API
  - name: SSE API
    description: Server-Sent Events
  - name: Chatbot
    description: Chatbot conversation
