import asyncio

from fastmcp import FastMCP
from loguru import logger
from starlette.requests import Request
from starlette.responses import JSONResponse

from action_server.core.config import Config
from action_server.core.logging import setup_logging
from action_server.middlewares import ErrorHandlingMiddleware, RequestLoggingMiddleware
from action_server.tools import (
    send_hello_tool,
    list_collections_tool,
    create_collection_tool,
    bulk_delete_collections_tool,
    bulk_duplicate_collections_tool,
    get_collection_by_id_tool,
    delete_collection_tool,
    update_collection_tool,
    delete_collection_access_tool,
    update_collection_access_tool,
    get_collection_columns_tool,
    get_collection_users_tool,
    invite_users_to_collection_tool,
    delete_collection_invitation_tool,
)
from database.init_db import init_db

instructions = (
    "This is the Provably Model Context Protocol (PMCP) Action Server. "
    "It acts as a trusted gateway for AI agents to interact with external systems. "
    "It handles agent identity, logs all actions for auditability, "
    "and ensures verifiable execution. Agents can use its tools for querying resources, "
    "executing trades, and managing their wallets. "
    "All actions performed through this server are persistently logged and "
    "provable by the PMCP Prover Server."
)

action_mcp = FastMCP(
    name="ActionServer",
    version="1.0.0",
    auth=None,
    lifespan=None,
    instructions=instructions,
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
    include_fastmcp_meta=True,
)


@action_mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request) -> JSONResponse:
    return JSONResponse(
        {
            "status": "healthy",
            "service": "pmcp-action-mcp-server",
            "version": Config.VERSION,
        }
    )


action_mcp.add_tool(send_hello_tool)

# Add Provably Collection tools
action_mcp.add_tool(list_collections_tool)
# action_mcp.add_tool(create_collection_tool)
# action_mcp.add_tool(bulk_delete_collections_tool)
# action_mcp.add_tool(bulk_duplicate_collections_tool)
# action_mcp.add_tool(get_collection_by_id_tool)
# action_mcp.add_tool(delete_collection_tool)
# action_mcp.add_tool(update_collection_tool)
# action_mcp.add_tool(delete_collection_access_tool)
# action_mcp.add_tool(update_collection_access_tool)
# action_mcp.add_tool(get_collection_columns_tool)
# action_mcp.add_tool(get_collection_users_tool)
# action_mcp.add_tool(invite_users_to_collection_tool)
# action_mcp.add_tool(delete_collection_invitation_tool)

action_mcp.add_middleware(ErrorHandlingMiddleware())
action_mcp.add_middleware(RequestLoggingMiddleware())


async def main():
    # --- On Startup ---
    setup_logging()
    logger.info("Starting PMCP Action Server")

    # _ = await init_db()

    logger.success(
        f"{Config.PROJECT_NAME} v{Config.VERSION} started successfully in {Config.ENVIRONMENT} mode."
    )

    await action_mcp.run_async(
        transport="http", port=Config.MCP_PORT, show_banner=False, log_level="critical"
    )

    # --- On Shutdown ---
    logger.info("Application is shutting down...")


if __name__ == "__main__":
    asyncio.run(main())
