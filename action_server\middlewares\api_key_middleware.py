"""
API Key Middleware for extracting Provably API keys from request headers.

This middleware should be implemented to extract the API key from request headers
and make it available to tools through the context.

TODO: Implement this middleware to:
1. Extract API key from request headers (e.g., 'X-API-Key' or 'Authorization')
2. Validate the API key format
3. Store the API key in the context for tools to access
4. <PERSON><PERSON> missing or invalid API keys appropriately
"""

from fastmcp.server.middleware import CallNext, Middleware, MiddlewareContext
from loguru import logger

from shared.exceptions import PMCPPermissionError


class APIKeyMiddleware(Middleware):
    """Middleware to extract and validate API keys from request headers."""
    
    def __init__(self, header_name: str = "X-API-Key"):
        """
        Initialize the API key middleware.
        
        Args:
            header_name: The header name to look for the API key (default: "X-API-Key")
        """
        self.header_name = header_name
    
    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        """Extract API key from headers and store in context."""
        
        # TODO: Implement header extraction logic
        # This is a placeholder implementation
        
        # Example of how to extract from headers:
        # headers = getattr(context, 'headers', {})
        # api_key = headers.get(self.header_name)
        
        # For now, we'll just log that this middleware needs implementation
        logger.debug("API Key middleware called - implementation needed")
        
        # TODO: Store API key in context for tools to access
        # Example: setattr(context, 'api_key', api_key)
        # Or: context.fastmcp_context.set_state("api_key", api_key)
        
        # TODO: Validate API key if needed
        # if not api_key:
        #     raise PMCPPermissionError("API key required")
        
        return await call_next(context)
    
    async def on_call_tool(self, context: MiddlewareContext, call_next: CallNext):
        """Ensure API key is available when tools are called."""
        
        # TODO: Verify API key is available in context before tool execution
        # This ensures tools can access the API key
        
        return await call_next(context)


# Example usage (not yet implemented):
# api_key_middleware = APIKeyMiddleware(header_name="X-Provably-API-Key")
# action_mcp.add_middleware(api_key_middleware)
