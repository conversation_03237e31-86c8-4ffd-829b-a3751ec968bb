import os
import tomllib
from pathlib import Path

from dotenv import load_dotenv
from pydantic import SecretStr, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict
from sqlalchemy import URL

load_dotenv()

# APP_ENV determines which .env.{APP_ENV} file to load.
if not (_app_env_value := os.getenv("APP_ENV")):
    raise ValueError("APP_ENV must be set to 'local', 'dev', or 'prod'")
APP_ENV: str = _app_env_value.lower()

# Base path for config files: project_root/config/
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
CONFIG_DIR = PROJECT_ROOT / "config"

# Get version from pyproject.toml as the single source of truth
with open(PROJECT_ROOT / "pyproject.toml", "rb") as f:
    data = tomllib.load(f)
__version__ = data["project"]["version"]

# Define .env files to load. Order matters: files listed later override earlier ones.
env_files_to_load = [
    CONFIG_DIR / ".env.default",  # Load default settings first
    CONFIG_DIR / f".env.{APP_ENV}",  # Load environment-specific non-secrets first
    PROJECT_ROOT / ".env",  # Load local secrets and overrides (takes precedence)
]

# Filter out non-existent files to avoid errors if an optional file is missing
# (e.g., .env.prod might not exist if all prod settings are via OS env vars)
existing_env_files = tuple(f for f in env_files_to_load if f.is_file())


class MCPConfig(BaseSettings):
    PROJECT_NAME: str = "PMCP Action Server"
    VERSION: str = __version__
    MCP_PORT: int = 8000

    # --- Environment ---
    ENVIRONMENT: str = APP_ENV
    IS_PROD_LIKE: bool = APP_ENV in ("prod")

    # --- Logging ---
    LOG_LEVEL: str = "INFO"

    # --- Database Settings ---
    DB_DRIVER_ASYNC: str = "postgresql+asyncpg"
    DB_NAME: str
    DB_HOST: str
    DB_PORT: int
    DB_USER: str
    DB_PASSWORD: SecretStr
    
    # --- Sentry ---
    SENTRY_DSN: SecretStr | None = None

    # --- Provably API ---
    PROVABLY_API_KEY: SecretStr
    PROVABLY_BASE_URL: str = "https://api.provably.ai"

    @computed_field
    @property
    def DB_URL(self) -> URL:
        return URL.create(
            drivername=self.DB_DRIVER_ASYNC,
            username=self.DB_USER,
            password=self.DB_PASSWORD.get_secret_value(),
            host=self.DB_HOST,
            port=self.DB_PORT,
            database=self.DB_NAME,
        )

    # pydantic-settings model configuration
    model_config = SettingsConfigDict(
        env_file=existing_env_files if existing_env_files else None,
        env_file_encoding="utf-8",
        extra="ignore",  # Ignore extra fields not defined in this model
        case_sensitive=True,
    )


Config = MCPConfig()  # type: ignore
