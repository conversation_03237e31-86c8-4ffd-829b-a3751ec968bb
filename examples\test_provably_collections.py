"""
Example script to test Provably Collections API integration.
This demonstrates how to use the PMCP Action Server to interact with Provably API.
"""

import asyncio
from fastmcp import Client

from action_server.contracts.provably.collections import (
    CollectionListRequest,
    CollectionQueryParams,
)


async def test_list_collections():
    """Test listing collections through PMCP Action Server."""
    
    # Create client connection to PMCP Action Server
    client = Client("http://127.0.0.1:8000/mcp")
    
    # Prepare request
    request = CollectionListRequest(
        api_key="your_test_api_key_here",  # Replace with actual API key
        query_params=CollectionQueryParams(
            page=0,
            page_size=10,
            query="test"
        )
    )
    
    try:
        async with client:
            # Call the provably_list_collections tool
            result = await client.call_tool(
                "provably_list_collections", 
                request.model_dump()
            )
            
            print("Collections retrieved successfully:")
            print(result)
            
    except Exception as e:
        print(f"Error calling Provably API: {e}")


async def test_get_collection_by_id():
    """Test getting a specific collection by ID."""
    
    client = Client("http://127.0.0.1:8000/mcp")
    
    # You'll need to replace this with an actual collection ID
    from uuid import UUID
    test_collection_id = UUID("12345678-1234-5678-9012-123456789012")
    
    try:
        async with client:
            result = await client.call_tool(
                "provably_get_collection_by_id",
                {
                    "api_key": "your_test_api_key_here",
                    "collection_id": str(test_collection_id)
                }
            )
            
            print("Collection retrieved successfully:")
            print(result)
            
    except Exception as e:
        print(f"Error getting collection: {e}")


if __name__ == "__main__":
    print("Testing Provably Collections API integration...")
    print("Make sure the PMCP Action Server is running on port 8000")
    print("And that you have valid PROVABLY_API_KEY in your environment")
    print()
    
    # Test listing collections
    print("1. Testing list collections...")
    asyncio.run(test_list_collections())
    
    print()
    
    # Test getting specific collection
    print("2. Testing get collection by ID...")
    asyncio.run(test_get_collection_by_id())
