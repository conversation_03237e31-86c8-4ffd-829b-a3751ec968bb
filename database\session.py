from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine

from action_server.core.config import Config
from utils.json_custom import custom_json_serializer

ASYNC_POOL_CONFIG = dict(
    echo=False,
    pool_size=10,
    max_overflow=10,
    pool_recycle=1800,
    pool_pre_ping=True,
    json_serializer=custom_json_serializer,
)

db_async_engine = create_async_engine(Config.DB_URL, **ASYNC_POOL_CONFIG)
db_async_session_maker = async_sessionmaker(
    db_async_engine, expire_on_commit=False, autocommit=False, autoflush=False
)

__all__ = [
    "db_async_session_maker",
    "db_async_engine",
]
