from enum import Enum
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field


# Enums
class AccessType(str, Enum):
    DRAFT = "draft"
    PUBLIC = "public"
    LIMITED_ACCESS = "limited_access"

class ColumnType(str, Enum):
    INTEGER = "integer"
    BIGINT = "bigint"
    SMALLINT = "smallint"
    DECIMAL = "decimal"
    TIME = "time"
    TIMESTAMP = "timestamp"
    DATE = "date"
    BOOLEAN = "boolean"
    UUID = "uuid"
    STRING = "string"

class InvitationStatus(str, Enum):
    JOINED = "joined"
    NOT_JOINED = "not_joined"

class Provider(str, Enum):
    POSTGRESQL = "postgresql"

class SourceTableColumnInvalidFieldType(str, Enum):
    NAME = "name"
    TYPE = "type"

class SortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"

class CollectionSortBy(str, Enum):
    NAME = "name"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    QUERY_COUNT = "query_count"
    COLUMN_COUNT = "column_count"
    ROW_COUNT = "row_count"
    CREDITS_EARNED = "credits_earned"

class CollectionColumnsSortBy(str, Enum):
    NAME = "name"
    TYPE = "type"

class CollectionUsersSortBy(str, Enum):
    EMAIL = "email"
    STATUS = "status"

class InvalidDependency(str, Enum):
    SOURCE_TABLE = "source_table"
    DATA_SOURCE = "data_source"


# Base Models
class CollectionAccessUpdateParams(BaseModel):
    access_type: AccessType

class CollectionBulkDeleteParams(BaseModel):
    ids: list[UUID]

class CollectionBulkDeleteResponse(BaseModel):
    ids: list[UUID]

class CollectionBulkDuplicateParams(BaseModel):
    ids: list[UUID]

class CollectionBulkDuplicateResponse(BaseModel):
    ids: list[UUID]

class CollectionColumnResponse(BaseModel):
    id: UUID
    name: str
    type: ColumnType
    description: str
    invalid_field: Optional[SourceTableColumnInvalidFieldType] = None

class CollectionColumnsListResponse(BaseModel):
    columns: list['SourceTableColumnResponse']

class CollectionCreateParams(BaseModel):
    name: str
    description: str
    data_source_id: UUID
    source_table_id: UUID
    column_ids: list[UUID]
    access_type: Optional[AccessType] = None

class CollectionResponseId(BaseModel):
    id: UUID

class CollectionUpdateParams(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    data_source_id: Optional[UUID] = None
    source_table_id: Optional[UUID] = None
    add_column_ids: Optional[list[UUID]] = None
    delete_column_ids: Optional[list[UUID]] = None
    access_type: Optional[AccessType] = None

class CollectionUserInvitationParams(BaseModel):
    emails: list[str]

class CollectionUserInvitationResponse(BaseModel):
    email: str
    status: InvitationStatus
    user_id: Optional[UUID] = None

class DataSourceResponseNoSchema(BaseModel):
    id: UUID
    name: str
    provider: Provider
    username: str
    uri: str
    is_valid: bool

class SourceTableResponse(BaseModel):
    name: str
    id: UUID
    schema_name: str
    table_name: str
    is_valid: bool

class SourceTableColumnResponse(BaseModel):
    id: UUID
    name: str
    data_type: ColumnType
    invalid_field: Optional[SourceTableColumnInvalidFieldType] = None

class CollectionResponse(BaseModel):
    id: UUID
    name: str
    description: str
    data_source: DataSourceResponseNoSchema
    source_table: SourceTableResponse
    owner_user_id: UUID
    column_count: int
    access_type: AccessType
    can_edit: bool
    query_count: int
    row_count: int
    created_at: str 
    updated_at: str 
    credits_earned: Optional[float] = None
    invalid_dependency: Optional[InvalidDependency] = None


# Request Models for Query Parameters
class CollectionQueryParams(BaseModel):
    type: Optional[str] = None
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str] = Field(None, description="Search query for collection name or description")
    sort_by: Optional[CollectionSortBy] = None
    is_valid: Optional[bool] = None
    sort_order: Optional[SortOrder] = None
    data_sources: Optional[list[UUID]] = None
    source_tables: Optional[list[UUID]] = None
    access_types: Optional[list[AccessType]] = None

class CollectionColumnsListQueryParams(BaseModel):
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str] = None
    sort_by: Optional[CollectionColumnsSortBy] = None
    sort_order: Optional[SortOrder] = None
    type: list[ColumnType]

class CollectionUsersListQueryParams(BaseModel):
    page: Optional[int] = Field(None, ge=0)
    page_size: Optional[int] = Field(None, ge=0)
    query: Optional[str] = None
    sort_by: Optional[CollectionUsersSortBy] = None
    sort_order: Optional[SortOrder] = None


# Tool Request Models (api_key will come from headers via middleware)
class CollectionListRequest(BaseModel):
    query_params: CollectionQueryParams

class CollectionCreateRequest(BaseModel):
    collection_data: CollectionCreateParams

class CollectionBulkDeleteRequest(BaseModel):
    bulk_delete_data: CollectionBulkDeleteParams

class CollectionBulkDuplicateRequest(BaseModel):
    bulk_duplicate_data: CollectionBulkDuplicateParams

class CollectionGetByIdRequest(BaseModel):
    collection_id: UUID

class CollectionDeleteRequest(BaseModel):
    collection_id: UUID

class CollectionUpdateRequest(BaseModel):
    collection_id: UUID
    update_data: CollectionUpdateParams

class CollectionAccessDeleteRequest(BaseModel):
    collection_id: UUID

class CollectionAccessUpdateRequest(BaseModel):
    collection_id: UUID
    access_data: CollectionAccessUpdateParams

class CollectionColumnsGetRequest(BaseModel):
    collection_id: UUID
    query_params: CollectionColumnsListQueryParams

class CollectionUsersGetRequest(BaseModel):
    collection_id: UUID
    query_params: CollectionUsersListQueryParams

class CollectionUsersInviteRequest(BaseModel):
    collection_id: UUID
    invitation_data: CollectionUserInvitationParams

class CollectionInvitationDeleteRequest(BaseModel):
    collection_id: UUID
    invitation_data: CollectionUserInvitationParams
