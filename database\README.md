# Database Migrations for PMCP

This directory contains the database migration system for the Provably Model Context Protocol (PMCP) project.

## Overview

The project uses [Alembic](https://alembic.sqlalchemy.org/) for database migrations, integrated with our SQLModel-based data models. The migration system supports both synchronous and asynchronous database operations.

## Setup

### Prerequisites

1. **PostgreSQL Database**: Ensure you have a PostgreSQL instance running
2. **Environment Configuration**: Set up your `.env` file with database credentials
3. **Dependencies**: All required packages are installed via `uv sync`

### Environment Variables

Make sure your `.env` file contains:

```bash
APP_ENV=local
DB_NAME=your_database_name
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
```

## Migration Commands

We provide a convenient migration management script at `database/migrations.py`. Here are the available commands:

### Create a New Migration

```bash
# Auto-generate migration from model changes
uv run python database/migrations.py create -m "Description of changes"

# Create empty migration (manual)
uv run python database/migrations.py create -m "Manual migration" --no-autogenerate
```

### Apply Migrations

```bash
# Upgrade to latest migration
uv run python database/migrations.py upgrade

# Upgrade to specific revision
uv run python database/migrations.py upgrade -r abc123
```

### Rollback Migrations

```bash
# Downgrade to specific revision
uv run python database/migrations.py downgrade -r abc123

# Downgrade one step
uv run python database/migrations.py downgrade -r -1
```

### Check Migration Status

```bash
# Show current migration status
uv run python database/migrations.py status

# Show current revision
uv run python database/migrations.py current

# Show migration history
uv run python database/migrations.py history
```

### Initialize Database

```bash
# Initialize database with all migrations (for fresh setups)
uv run python database/migrations.py init-db
```

## Direct Alembic Commands

You can also use Alembic directly:

```bash
# Create migration
uv run alembic revision --autogenerate -m "Migration message"

# Apply migrations
uv run alembic upgrade head

# Show current revision
uv run alembic current

# Show history
uv run alembic history
```

## Migration Workflow

### 1. Making Model Changes

When you modify SQLModel classes in `shared/models/`, follow this workflow:

1. **Update the model** in the appropriate file (e.g., `shared/models/action_history.py`)
2. **Create a migration**:
   ```bash
   uv run python database/migrations.py create -m "Add new field to ActionHistory"
   ```
3. **Review the generated migration** in `alembic/versions/`
4. **Apply the migration**:
   ```bash
   uv run python database/migrations.py upgrade
   ```

### 2. Adding New Models

1. **Create the new model** in `shared/models/`
2. **Import the model** in `alembic/env.py` (if not auto-imported)
3. **Generate migration**:
   ```bash
   uv run python database/migrations.py create -m "Add NewModel table"
   ```
4. **Apply migration**:
   ```bash
   uv run python database/migrations.py upgrade
   ```

## File Structure

```
database/
├── README.md              # This file
├── migrations.py          # Migration management script
├── base.py               # SQLModel base configuration
├── session.py            # Database session management
└── init_db.py            # Database initialization

alembic/
├── env.py                # Alembic environment configuration
├── script.py.mako        # Migration template
└── versions/             # Migration files
    └── *.py              # Individual migration files

alembic.ini               # Alembic configuration
```

## Configuration Details

### Alembic Environment (`alembic/env.py`)

The environment is configured to:
- Use our project's configuration system
- Support both async and sync database operations
- Auto-import all SQLModel models
- Handle connection failures gracefully
- Use PostgreSQL-specific features (JSONB, UUID, etc.)

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **Migration Conflicts**
   - Use `uv run alembic history` to see migration tree
   - Resolve conflicts by creating merge migrations

3. **Model Import Errors**
   - Ensure all models are imported in `alembic/env.py`
   - Check for circular imports

### Recovery Commands

```bash
# Mark current database state as up-to-date (dangerous!)
uv run alembic stamp head

# Show SQL without executing
uv run alembic upgrade head --sql

# Downgrade to base (removes all tables!)
uv run alembic downgrade base
```

## Best Practices

1. **Always review generated migrations** before applying
2. **Test migrations on development data** first
3. **Create backups** before major schema changes
4. **Use descriptive migration messages**
5. **Keep migrations small and focused**
6. **Never edit applied migrations** - create new ones instead

## Integration with Application

The migration system integrates with the main application through:
- Shared configuration (`action_server.core.config`)
- Common database session management
- Consistent model definitions
- Environment-based setup

For production deployments, migrations should be run as part of the deployment process before starting the application servers.
